"""
Enhanced Textbook Service - Cải tiến xử lý sách gi<PERSON>o khoa với OCR và LLM
Trả về cấu trúc: S<PERSON><PERSON> → Chương → Bài → Nội dung
"""

import logging
import asyncio
import json
from typing import Dict, Any, List, Optional
from concurrent.futures import ThreadPoolExecutor
import fitz  # PyMuPDF
from PIL import Image
import io

from app.services.simple_ocr_service import simple_ocr_service
from app.services.llm_service import llm_service

logger = logging.getLogger(__name__)


class EnhancedTextbookService:
    """Service cải tiến để xử lý sách giáo khoa với OCR và LLM"""

    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=4)

    async def process_textbook_to_structure(
        self,
        pdf_content: bytes,
        filename: str,
        book_metadata: Optional[Dict[str, Any]] = None,
        lesson_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Xử lý PDF sách giáo khoa và trả về cấu trúc hoàn chỉnh

        Args:
            pdf_content: Nội dung PDF
            filename: Tên file
            book_metadata: Metadata sách (title, subject, grade, etc.)
            lesson_id: ID bài học tùy chọn để liên kết với lesson cụ thể

        Returns:
            Dict với cấu trúc: book -> chapters -> lessons -> content
        """
        try:
            logger.info(f"🚀 Starting enhanced textbook processing: {filename}")

            # Step 1: Extract all pages with OCR
            logger.info("📄 Extracting pages with OCR...")
            pages_data = await self._extract_pages_with_ocr(pdf_content)
            logger.info(f"✅ Extracted {len(pages_data)} pages")

            # Skip image analysis to improve speed
            logger.info("⚡ Skipping image analysis for faster processing")

            # Step 3: Analyze book structure with LLM
            logger.info("🧠 Analyzing book structure...")
            book_structure = await self._analyze_book_structure_enhanced(
                pages_data, book_metadata
            )
            logger.info(
                f"📚 Detected {len(book_structure.get('chapters', []))} chapters"
            )

            # Step 4: Build final structure with content and lesson IDs
            logger.info("🔄 Building final lesson structure...")
            processed_book = await self._build_final_structure(
                book_structure,
                pages_data,
                book_metadata or {},
                lesson_id,  # Pass lesson_id
            )

            logger.info("✅ Textbook processing completed successfully")

            # Skip image extraction for faster processing
            images_data = []
            logger.info("⚡ Skipping image extraction for faster processing")

            # Refine content with OpenRouter LLM before saving to Qdrant
            logger.info("🤖 Refining content with OpenRouter LLM...")
            refined_book_structure = await self.refine_content_with_llm(processed_book)
            logger.info("✅ Content refinement completed")

            # Prepare clean structure for Qdrant
            clean_book_structure = self.prepare_structure_for_qdrant(refined_book_structure)

            # Tính toán thống kê đơn giản cho 1 bài học
            total_lessons = sum(len(ch.get("lessons", [])) for ch in refined_book_structure.get("chapters", []))

            return {
                "success": True,
                "book": refined_book_structure,  # Full structure with refined content and image data
                "clean_book_structure": clean_book_structure,  # Structure without image data for Qdrant
                "images_data": images_data,  # Separate image data for external storage
                "total_pages": len(pages_data),
                "total_chapters": len(refined_book_structure.get("chapters", [])),
                "total_lessons": total_lessons,
                "total_images": len(images_data),
                "message": f"Textbook processed successfully with LLM content refinement ({total_lessons} lesson{'s' if total_lessons != 1 else ''})",
            }

        except Exception as e:
            logger.error(f"❌ Error processing textbook: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to process textbook",
            }

    async def _extract_pages_with_ocr(self, pdf_content: bytes) -> List[Dict[str, Any]]:
        """Extract tất cả pages với OCR nếu cần"""

        def extract_page_data():
            doc = fitz.open(stream=pdf_content, filetype="pdf")
            pages_data = []

            for page_num in range(doc.page_count):
                page = doc[page_num]

                # Extract text normally first
                text = page.get_text("text")  # type: ignore

                # Skip image extraction for faster processing
                images = []

                pages_data.append(
                    {
                        "page_number": page_num + 1,
                        "text": text,
                        "images": images,
                        "has_text": len(text.strip()) > 50,
                    }
                )

            doc.close()
            return pages_data

        # Extract pages in background thread
        pages_data = await asyncio.get_event_loop().run_in_executor(
            self.executor, extract_page_data
        )

        # Apply OCR to pages with insufficient text
        ocr_tasks = []
        for page in pages_data:
            if not page["has_text"]:
                ocr_tasks.append(self._apply_ocr_to_page(page, pdf_content))

        if ocr_tasks:
            logger.info(
                f"🔍 Applying OCR to {len(ocr_tasks)} pages with insufficient text"
            )
            ocr_results = await asyncio.gather(*ocr_tasks, return_exceptions=True)

            # Update pages with OCR results
            ocr_index = 0
            for page in pages_data:
                if not page["has_text"] and ocr_index < len(ocr_results):
                    if not isinstance(ocr_results[ocr_index], Exception):
                        page["text"] = ocr_results[ocr_index]
                        page["ocr_applied"] = True
                    ocr_index += 1

        return pages_data

    async def _apply_ocr_to_page(
        self, page_data: Dict[str, Any], pdf_content: bytes
    ) -> str:
        """Apply OCR to a specific page"""
        try:
            # Use existing OCR service for this page
            page_num = page_data["page_number"]

            # Extract just this page as bytes
            def extract_single_page():
                doc = fitz.open(stream=pdf_content, filetype="pdf")
                page = doc[page_num - 1]

                # Convert page to image
                mat = fitz.Matrix(2.0, 2.0)  # 2x zoom for better OCR
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("png")
                pix = None
                doc.close()

                return img_data

            img_data = await asyncio.get_event_loop().run_in_executor(
                self.executor, extract_single_page
            )

            # Apply OCR using PIL and simple_ocr_service logic
            image = Image.open(io.BytesIO(img_data))

            # Use simple OCR service's OCR logic
            if (
                hasattr(simple_ocr_service, "easyocr_reader")
                and simple_ocr_service.easyocr_reader
            ):
                import numpy as np

                results = simple_ocr_service.easyocr_reader.readtext(np.array(image))
                text_parts = []
                for result in results:
                    if isinstance(result, (list, tuple)) and len(result) >= 2:
                        # EasyOCR returns [bbox, text, confidence]
                        text_parts.append(str(result[1]))
                return " ".join(text_parts)
            else:
                # Fallback to Tesseract
                import pytesseract

                return pytesseract.image_to_string(
                    image, config=simple_ocr_service.tesseract_config
                )

        except Exception as e:
            logger.error(f"OCR failed for page {page_data['page_number']}: {e}")
            return ""

    async def _analyze_book_structure_enhanced(
        self,
        pages_data: List[Dict[str, Any]],
        book_metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """Phân tích cấu trúc sách với LLM cải tiến"""

        if not llm_service.is_available():
            logger.warning("LLM not available, using pattern-based analysis")
            return await self._pattern_based_structure_analysis(
                pages_data, book_metadata
            )

        # Tạo text sample từ các trang để phân tích
        sample_text = ""
        for i, page in enumerate(pages_data[:20]):  # Lấy 20 trang đầu để phân tích
            if page["text"].strip():
                sample_text += f"\n--- Trang {page['page_number']} ---\n{page['text'][:500]}"  # 500 chars per page

        prompt = f"""
Bạn là chuyên gia phân tích sách giáo khoa Việt Nam. Phân tích nội dung và trả về cấu trúc chính xác.

THÔNG TIN SÁCH:
- Tổng số trang: {len(pages_data)}
- Metadata: {json.dumps(book_metadata or {}, ensure_ascii=False)}

NỘI DUNG SAMPLE:
{sample_text}

YÊU CẦU:
1. Xác định tiêu đề sách, môn học, lớp
2. Tìm tất cả CHƯƠNG (Chapter) trong sách
3. Tìm tất cả BÀI HỌC (Lesson) trong mỗi chương
4. Xác định trang bắt đầu và kết thúc cho mỗi chương/bài
5. Trả về JSON chuẩn

JSON FORMAT:
{{
  "book_info": {{
    "title": "Tên sách chính xác",
    "subject": "Môn học (Toán/Lý/Hóa/...)",
    "grade": "Lớp (10/11/12)",
    "total_pages": {len(pages_data)}
  }},
  "chapters": [
    {{
      "chapter_id": "chapter_01",
      "chapter_title": "Tên chương chính xác",
      "start_page": 1,
      "end_page": 20,
      "lessons": [
        {{
          "lesson_id": "lesson_01_01",
          "lesson_title": "Tên bài học chính xác",
          "start_page": 1,
          "end_page": 5
        }}
      ]
    }}
  ]
}}

Trả về JSON:"""

        try:
            if not llm_service.model:
                raise Exception("LLM model not available")

            response = llm_service.model.generate_content(prompt)
            json_text = response.text.strip()

            # Clean JSON - cải thiện việc xử lý
            if json_text.startswith("```json"):
                json_text = json_text[7:]
            if json_text.startswith("```"):
                json_text = json_text[3:]
            if json_text.endswith("```"):
                json_text = json_text[:-3]

            # Tìm JSON hợp lệ trong response
            json_text = json_text.strip()

            # Tìm vị trí bắt đầu và kết thúc của JSON
            start_idx = json_text.find("{")
            if start_idx == -1:
                raise ValueError("No JSON object found in response")

            # Tìm vị trí kết thúc JSON bằng cách đếm dấu ngoặc
            brace_count = 0
            end_idx = start_idx
            for i, char in enumerate(json_text[start_idx:], start_idx):
                if char == "{":
                    brace_count += 1
                elif char == "}":
                    brace_count -= 1
                    if brace_count == 0:
                        end_idx = i + 1
                        break

            # Extract JSON hợp lệ
            clean_json = json_text[start_idx:end_idx]

            structure = json.loads(clean_json)

            # Validate structure
            if "chapters" in structure and len(structure["chapters"]) > 0:
                logger.info(f"LLM detected {len(structure['chapters'])} chapters")
                return structure
            else:
                logger.warning("LLM returned invalid structure, using fallback")
                return await self._pattern_based_structure_analysis(
                    pages_data, book_metadata
                )

        except Exception as e:
            logger.error(f"LLM structure analysis failed: {e}")
            logger.debug(
                f"Raw LLM response: {response.text[:500] if 'response' in locals() else 'No response'}"
            )
            return await self._pattern_based_structure_analysis(
                pages_data, book_metadata
            )

    async def _pattern_based_structure_analysis(
        self,
        pages_data: List[Dict[str, Any]],
        book_metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """Phân tích cấu trúc dựa trên pattern matching"""

        total_pages = len(pages_data)

        # Extract book info from metadata or first pages
        book_info = {
            "title": book_metadata.get("title", "Sách giáo khoa")
            if book_metadata
            else "Sách giáo khoa",
            "subject": book_metadata.get("subject", "Chưa xác định")
            if book_metadata
            else "Chưa xác định",
            "grade": book_metadata.get("grade", "Chưa xác định")
            if book_metadata
            else "Chưa xác định",
            "total_pages": total_pages,
        }

        # Find chapters and lessons using pattern matching
        chapters = []
        current_chapter = None
        current_lesson = None

        for page in pages_data:
            lines = page["text"].split("\n")

            # Look for chapter patterns
            for line in lines:
                line_clean = line.strip()
                if len(line_clean) > 5 and len(line_clean) < 100:
                    # Chapter detection
                    if any(
                        pattern in line_clean.lower()
                        for pattern in ["chương", "chapter", "phần", "bài tập chương"]
                    ):
                        # Save previous chapter
                        if current_chapter:
                            chapters.append(current_chapter)

                        # Start new chapter
                        chapter_num = len(chapters) + 1
                        current_chapter = {
                            "chapter_id": f"chapter_{chapter_num:02d}",
                            "chapter_title": line_clean,
                            "start_page": page["page_number"],
                            "end_page": page["page_number"],
                            "lessons": [],
                        }
                        current_lesson = None

                    # Lesson detection
                    elif (
                        any(
                            pattern in line_clean.lower()
                            for pattern in ["bài", "lesson", "tiết"]
                        )
                        and current_chapter
                    ):
                        # Save previous lesson
                        if current_lesson:
                            current_chapter["lessons"].append(current_lesson)

                        # Start new lesson
                        lesson_num = len(current_chapter["lessons"]) + 1
                        current_lesson = {
                            "lesson_id": f"lesson_{len(chapters)+1:02d}_{lesson_num:02d}",
                            "lesson_title": line_clean,
                            "start_page": page["page_number"],
                            "end_page": page["page_number"],
                        }

            # Update end pages
            if current_chapter:
                current_chapter["end_page"] = page["page_number"]
            if current_lesson:
                current_lesson["end_page"] = page["page_number"]

        # Add final chapter and lesson
        if current_lesson and current_chapter:
            current_chapter["lessons"].append(current_lesson)
        if current_chapter:
            chapters.append(current_chapter)

        # If no chapters found, create default structure
        if not chapters:
            chapters = self._create_default_structure(total_pages)

        return {"book_info": book_info, "chapters": chapters}

    def _create_default_structure(self, total_pages: int) -> List[Dict[str, Any]]:
        """Tạo cấu trúc mặc định khi không detect được"""

        chapters = []
        pages_per_chapter = max(total_pages // 3, 10)  # Ít nhất 3 chương

        for chapter_num in range(1, 4):  # 3 chương
            start_page = (chapter_num - 1) * pages_per_chapter + 1
            end_page = min(chapter_num * pages_per_chapter, total_pages)

            if start_page > total_pages:
                break

            # Tạo 2-3 bài trong mỗi chương
            lessons = []
            pages_per_lesson = max((end_page - start_page + 1) // 3, 3)

            for lesson_num in range(1, 4):  # 3 bài mỗi chương
                lesson_start = start_page + (lesson_num - 1) * pages_per_lesson
                lesson_end = min(
                    start_page + lesson_num * pages_per_lesson - 1, end_page
                )

                if lesson_start > end_page:
                    break

                lessons.append(
                    {
                        "lesson_id": f"lesson_{chapter_num:02d}_{lesson_num:02d}",
                        "lesson_title": f"Bài {lesson_num}",
                        "start_page": lesson_start,
                        "end_page": lesson_end,
                    }
                )

            chapters.append(
                {
                    "chapter_id": f"chapter_{chapter_num:02d}",
                    "chapter_title": f"Chương {chapter_num}",
                    "start_page": start_page,
                    "end_page": end_page,
                    "lessons": lessons,
                }
            )

        return chapters

    async def _process_lessons_content(
        self,
        book_structure: Dict[str, Any],
        pages_data: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """Xử lý nội dung chi tiết cho từng bài học"""

        processed_book = {
            "book_info": book_structure.get("book_info", {}),
            "chapters": [],
        }

        for chapter in book_structure.get("chapters", []):
            processed_chapter = {
                "chapter_id": chapter["chapter_id"],
                "chapter_title": chapter["chapter_title"],
                "start_page": chapter["start_page"],
                "end_page": chapter["end_page"],
                "lessons": [],
            }

            for lesson in chapter.get("lessons", []):
                logger.info(f"Processing lesson: {lesson['lesson_title']}")

                # Extract content for this lesson
                lesson_content = await self._extract_lesson_content(lesson, pages_data)

                processed_lesson = {
                    "lesson_id": lesson["lesson_id"],
                    "lesson_title": lesson["lesson_title"],
                    "start_page": lesson["start_page"],
                    "end_page": lesson["end_page"],
                    "content": lesson_content,
                }

                processed_chapter["lessons"].append(processed_lesson)

            processed_book["chapters"].append(processed_chapter)

        return processed_book

    async def _extract_lesson_content(
        self, lesson: Dict[str, Any], pages_data: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Extract nội dung text của một bài học (bỏ qua hình ảnh để tăng tốc)"""

        start_page = lesson["start_page"]
        end_page = lesson["end_page"]

        # Chỉ collect text content
        lesson_text = ""
        lesson_pages = []

        for page_num in range(start_page, end_page + 1):
            # Find page data
            page_data = None
            for page in pages_data:
                if page["page_number"] == page_num:
                    page_data = page
                    break

            if not page_data:
                continue

            lesson_pages.append(page_num)

            # Add text content only
            if page_data["text"].strip():
                lesson_text += f"\n--- Trang {page_num} ---\n{page_data['text'].strip()}\n"

        return {
            "text": lesson_text.strip(),
            "pages": lesson_pages,
            "total_pages": len(lesson_pages),
        }







    async def _build_final_structure(
        self,
        analysis_result: Dict[str, Any],
        pages_data: List[Dict[str, Any]],
        book_metadata: Dict[str, Any],
        external_lesson_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Build final book structure from analysis result with lessonID and improved image handling"""

        book_structure = {
            "title": analysis_result.get("book_info", {}).get(
                "title", book_metadata["title"]
            ),
            "subject": analysis_result.get("book_info", {}).get(
                "subject", book_metadata["subject"]
            ),
            "grade": analysis_result.get("book_info", {}).get(
                "grade", book_metadata["grade"]
            ),
            "chapters": [],
        }  # Process chapters and lessons
        lesson_counter = 0  # Track total lesson count for lesson_id variants
        for chapter_index, chapter in enumerate(analysis_result.get("chapters", [])):
            chapter_obj = {
                "chapter_id": f"chapter_{chapter_index:02d}",  # Add chapter_id
                "title": chapter.get("chapter_title", "Chương không xác định"),
                "lessons": [],
            }

            # Extract lessons
            for lesson in chapter.get("lessons", []):
                lesson_content = ""
                start_page = lesson.get("start_page", 1)
                end_page = lesson.get("end_page", start_page)

                # Collect content and images from lesson pages
                for page in pages_data:
                    page_num = page.get("page_number", 0)
                    if start_page <= page_num <= end_page:
                        lesson_content += page.get("text", "") + "\n"  # Generate lesson ID - use external_lesson_id if provided, otherwise create UUID
                if external_lesson_id:
                    if lesson_counter == 0:
                        lesson_id = external_lesson_id
                        logger.info(f"Using provided lesson_id: {lesson_id}")
                    else:
                        lesson_id = f"{external_lesson_id}_{lesson_counter}"
                        logger.info(f"Using variant lesson_id: {lesson_id}")
                else:
                    lesson_id = str(uuid.uuid4())
                    logger.debug(f"Generated new lesson_id: {lesson_id}")

                lesson_counter += 1  # Increment for next lesson

                lesson_obj = {
                    "lesson_id": lesson_id,  # Add unique lesson ID
                    "title": lesson.get("lesson_title", "Bài học không xác định"),
                    "content": lesson_content.strip(),
                    "page_numbers": list(range(start_page, end_page + 1)),
                }
                chapter_obj["lessons"].append(lesson_obj)

            book_structure["chapters"].append(chapter_obj)

        return book_structure



    async def refine_content_with_llm(
        self, book_structure: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Gửi nội dung đến OpenRouter LLM để lọc và chỉnh sửa nội dung bài giảng

        Tối ưu cho việc xử lý 1 bài học duy nhất trong mỗi PDF
        """
        try:
            from app.services.openrouter_service import OpenRouterService

            openrouter_service = OpenRouterService()
            if not openrouter_service.available:
                logger.warning("OpenRouter service not available, skipping content refinement")
                return book_structure

            import copy
            refined_structure = copy.deepcopy(book_structure)

            logger.info("🤖 Starting content refinement with OpenRouter LLM...")

            # Tìm bài học đầu tiên để xử lý (vì chỉ có 1 bài/PDF)
            first_lesson = None
            for chapter in refined_structure.get("chapters", []):
                for lesson in chapter.get("lessons", []):
                    if lesson.get("content"):
                        first_lesson = lesson
                        break
                if first_lesson:
                    break

            if not first_lesson:
                logger.warning("No lesson content found to refine")
                return book_structure

            # Tập hợp tất cả text content từ lesson
            all_text_content = []
            for content_item in first_lesson.get("content", []):
                if content_item.get("type") == "text" and content_item.get("text"):
                    all_text_content.append(content_item.get("text", ""))

            if not all_text_content:
                logger.warning("No text content found in lesson")
                return book_structure

            # Ghép nội dung lại
            combined_content = "\n\n".join(all_text_content)

            # Tạo prompt để LLM lọc nội dung
            prompt = f"""
Bạn là chuyên gia giáo dục, hãy lọc và chỉnh sửa nội dung bài giảng sau để chỉ giữ lại những thông tin quan trọng và chi tiết của bài giảng.

YÊU CẦU:
1. Loại bỏ thông tin không liên quan đến nội dung bài học (header, footer, số trang, thông tin xuất bản, etc.)
2. Giữ lại toàn bộ kiến thức chính, khái niệm, định nghĩa, công thức, ví dụ
3. Giữ lại các bài tập, câu hỏi, hoạt động thực hành
4. Sắp xếp nội dung theo logic rõ ràng, dễ hiểu
5. Đảm bảo nội dung đầy đủ và chính xác, không bỏ sót thông tin quan trọng
6. Trả về nội dung đã được chỉnh sửa bằng tiếng Việt

TIÊU ĐỀ BÀI HỌC: {first_lesson.get("lesson_title", "Không có tiêu đề")}

NỘI DUNG GỐC:
{combined_content[:3000]}  # Giới hạn 3000 ký tự để tránh vượt quá token limit

Hãy trả về nội dung đã được lọc và chỉnh sửa:
"""

            # Gọi OpenRouter API
            result = await openrouter_service.generate_content(
                prompt=prompt,
                temperature=0.1,
                max_tokens=2048
            )

            if result.get("success") and result.get("text"):
                refined_content = result.get("text", "").strip()

                # Cập nhật nội dung đã được chỉnh sửa
                first_lesson["content"] = [
                    {
                        "type": "text",
                        "text": refined_content,
                        "page": first_lesson["content"][0].get("page", 1) if first_lesson["content"] else 1,
                        "section": "refined_content",
                        "refined_by_llm": True
                    }
                ]

                logger.info(f"✅ Refined content for lesson: {first_lesson.get('lesson_title', 'Unknown')}")
            else:
                logger.warning(f"❌ Failed to refine content for lesson: {first_lesson.get('lesson_title', 'Unknown')}")

            logger.info("🎯 Content refinement completed")
            return refined_structure

        except Exception as e:
            logger.error(f"Error in content refinement: {e}")
            # Trả về cấu trúc gốc nếu có lỗi
            return book_structure

    def prepare_structure_for_qdrant(
        self, book_structure: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Prepare book structure for Qdrant storage (no image processing needed)"""
        # Since we skip image processing, just return the structure as-is
        return book_structure


# Global instance
enhanced_textbook_service = EnhancedTextbookService()

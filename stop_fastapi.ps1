# PowerShell script để tắt FastAPI server
Write-Host "🛑 Stopping FastAPI Server..." -ForegroundColor Yellow

# Tìm process đang sử dụng port 8000
$process = Get-NetTCPConnection -LocalPort 8000 -ErrorAction SilentlyContinue | Select-Object -ExpandProperty OwningProcess
if ($process) {
    Write-Host "📊 Found process with PID: $process" -ForegroundColor Green
    
    # Lấy thông tin process
    $processInfo = Get-Process -Id $process -ErrorAction SilentlyContinue
    if ($processInfo) {
        Write-Host "🔍 Process: $($processInfo.Name)" -ForegroundColor Cyan
        
        # Dừng process
        Stop-Process -Id $process -Force -ErrorAction SilentlyContinue
        Write-Host "✅ FastAPI server stopped successfully" -ForegroundColor Green
    }
} else {
    Write-Host "❌ No process found on port 8000" -ForegroundColor Red
}

# Tìm và dừng tất cả process uvicorn
$uvicornProcesses = Get-Process python -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like "*uvicorn*" }
if ($uvicornProcesses) {
    Write-Host "🛑 Stopping uvicorn processes..." -ForegroundColor Yellow
    $uvicornProcesses | Stop-Process -Force
    Write-Host "✅ All uvicorn processes stopped" -ForegroundColor Green
} else {
    Write-Host "✅ No uvicorn processes found" -ForegroundColor Green
}

Write-Host "🎯 FastAPI shutdown complete!" -ForegroundColor Green
Read-Host "Press Enter to continue..."

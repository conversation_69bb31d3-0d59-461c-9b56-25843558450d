#!/usr/bin/env python3
"""
Simple test để kiểm tra API
"""

import requests
import sys

def test_api():
    """Test API đơn giản"""
    print("🧪 Testing API...")
    
    try:
        # Test health check
        response = requests.get("http://localhost:8000/health", timeout=10)
        print(f"Health check: {response.status_code}")
        
        # Test get textbook
        response = requests.get("http://localhost:8000/api/v1/pdf/textbook/1", timeout=10)
        print(f"Get textbook: {response.status_code}")
        print(f"Response: {response.text[:200]}...")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    test_api()

#!/usr/bin/env python3
"""
Tạo file PDF test để kiểm tra hệ thống
"""

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter

def create_test_pdf():
    """Tạo file PDF test"""
    filename = "test_lesson_hoa12.pdf"
    
    # Tạo PDF
    c = canvas.Canvas(filename, pagesize=letter)
    
    # Thêm nội dung
    c.drawString(100, 750, "BÀI HỌC TEST - HÓA HỌC 12")
    c.drawString(100, 720, "")
    c.drawString(100, 690, "1. NGUYÊN TỬ VÀ CẤU TẠO NGUYÊN TỬ")
    c.drawString(100, 660, "")
    c.drawString(100, 630, "Nguyên tử là đơn vị cơ bản của vật chất.")
    c.drawString(100, 600, "Nguyên tử gồm có:")
    c.drawString(120, 570, "- Hạt nhân: chứa proton và neutron")
    c.drawString(120, 540, "- Vỏ electron: chứa các electron")
    c.drawString(100, 510, "")
    c.drawString(100, 480, "2. CÁC HẠT CƠ BẢN")
    c.drawString(100, 450, "- Proton: mang điện tích dương (+1)")
    c.drawString(100, 420, "- Neutron: không mang điện tích (0)")
    c.drawString(100, 390, "- Electron: mang điện tích âm (-1)")
    c.drawString(100, 360, "")
    c.drawString(100, 330, "3. TÍNH CHẤT")
    c.drawString(100, 300, "- Khối lượng proton ≈ khối lượng neutron")
    c.drawString(100, 270, "- Khối lượng electron rất nhỏ")
    c.drawString(100, 240, "- Số proton = số electron (nguyên tử trung hòa)")
    
    c.save()
    print(f"✅ Created test PDF: {filename}")
    return filename

if __name__ == "__main__":
    create_test_pdf()

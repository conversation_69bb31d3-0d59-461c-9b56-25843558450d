@echo off
REM Script để tắt FastAPI server cho PlanBook AI

echo [STOP] Stopping FastAPI Server for PlanBook AI...
echo.

REM Tìm và dừng process uvicorn/FastAPI đang chạy trên port 8000
echo [INFO] Finding FastAPI processes...

REM Tìm process đang sử dụng port 8000
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8000') do (
    set pid=%%a
    goto :found
)

:found
if defined pid (
    echo [INFO] Found process with PID: %pid%
    
    REM Kiểm tra xem process có phải là uvicorn/python không
    for /f "tokens=1" %%b in ('tasklist /fi "PID eq %pid%" /fo table /nh') do (
        set processname=%%b
        goto :checkprocess
    )
    
    :checkprocess
    if /i "%processname%"=="python.exe" (
        echo [OK] Confirmed Python/FastAPI process found
        echo [STOP] Stopping FastAPI server (PID: %pid%)...
        taskkill /PID %pid% /F >nul 2>&1
        if errorlevel 1 (
            echo [ERROR] Failed to stop process
        ) else (
            echo [OK] FastAPI server stopped successfully
        )
    ) else (
        echo [WARN] Process on port 8000 is not Python/FastAPI: %processname%
        echo [STOP] Attempting to stop anyway...
        taskkill /PID %pid% /F >nul 2>&1
        if errorlevel 1 (
            echo [ERROR] Failed to stop process
        ) else (
            echo [OK] Process stopped successfully
        )
    )
) else (
    echo [ERROR] No process found running on port 8000
    echo [INFO] FastAPI server may already be stopped
)

echo.

REM Tìm tất cả process uvicorn đang chạy
echo [INFO] Checking for any remaining uvicorn processes...
tasklist /fi "imagename eq python.exe" | findstr uvicorn >nul 2>&1
if not errorlevel 1 (
    echo [STOP] Stopping remaining uvicorn processes...
    wmic process where "commandline like '%%uvicorn%%'" delete >nul 2>&1
    echo [OK] All uvicorn processes stopped
) else (
    echo [OK] No uvicorn processes found
)

echo.
echo [COMPLETE] FastAPI shutdown complete!
echo [INFO] You can start the server again using: start_fastapi.bat
echo.

pause

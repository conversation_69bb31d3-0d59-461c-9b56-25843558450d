#!/usr/bin/env python3
"""
Test script để upload PDF và kiểm tra hệ thống đã tối ưu
"""

import requests
import json

def test_pdf_upload():
    """Test upload PDF với hệ thống đã tối ưu"""
    
    # Tạo một file PDF test đơn giản
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter
    import io
    
    # Tạo PDF content trong memory
    buffer = io.BytesIO()
    p = canvas.Canvas(buffer, pagesize=letter)
    
    # Thêm nội dung test
    p.drawString(100, 750, "BÀI HỌC TEST - HÓA HỌC 12")
    p.drawString(100, 720, "")
    p.drawString(100, 690, "1. NGUYÊN TỬ VÀ CẤU TẠO NGUYÊN TỬ")
    p.drawString(100, 660, "")
    p.drawString(100, 630, "Nguyên tử là đơn vị cơ bản của vật chất.")
    p.drawString(100, 600, "Nguyên tử gồm có:")
    p.drawString(120, 570, "- H<PERSON><PERSON> nhân: chứa proton và neutron")
    p.drawString(120, 540, "- Vỏ electron: chứa các electron")
    p.drawString(100, 510, "")
    p.drawString(100, 480, "2. CÁC HẠT CƠ BẢN")
    p.drawString(100, 450, "- Proton: mang điện tích dương (+1)")
    p.drawString(100, 420, "- Neutron: không mang điện tích (0)")
    p.drawString(100, 390, "- Electron: mang điện tích âm (-1)")
    
    p.showPage()
    p.save()
    
    # Lấy PDF content
    buffer.seek(0)
    pdf_content = buffer.getvalue()
    buffer.close()
    
    print("📄 Created test PDF content")
    
    # Upload PDF
    url = "http://localhost:8000/api/v1/pdf/upload"
    
    files = {
        'file': ('test_lesson.pdf', pdf_content, 'application/pdf')
    }
    
    data = {
        'lesson_id': 'test_optimized_001',
        'create_embeddings': 'true'
    }
    
    print("🚀 Uploading PDF to optimized system...")
    
    try:
        response = requests.post(url, files=files, data=data, timeout=300)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Upload successful!")
            print(f"📊 Task ID: {result.get('task_id')}")
            print(f"📝 Message: {result.get('message')}")
            
            # Kiểm tra task status
            task_id = result.get('task_id')
            if task_id:
                check_task_status(task_id)
                
        else:
            print(f"❌ Upload failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def check_task_status(task_id):
    """Kiểm tra trạng thái task"""
    url = f"http://localhost:8000/api/v1/pdf/task-status/{task_id}"
    
    print(f"🔍 Checking task status: {task_id}")
    
    try:
        response = requests.get(url)
        if response.status_code == 200:
            result = response.json()
            print(f"📊 Task Status: {result.get('status')}")
            print(f"📈 Progress: {result.get('progress', 0)}%")
            print(f"💬 Message: {result.get('message')}")
            
            if result.get('status') == 'completed':
                print("✅ Task completed successfully!")
                
                # Test lấy nội dung
                test_get_content(result.get('result', {}).get('lesson_id', 'test_optimized_001'))
            elif result.get('status') == 'failed':
                print("❌ Task failed!")
                print(f"Error: {result.get('error')}")
        else:
            print(f"❌ Status check failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error checking status: {e}")

def test_get_content(lesson_id):
    """Test lấy nội dung lesson"""
    url = f"http://localhost:8000/api/v1/pdf/textbook/{lesson_id}"
    
    print(f"📖 Testing content retrieval for lesson: {lesson_id}")
    
    try:
        response = requests.get(url)
        if response.status_code == 200:
            result = response.json()
            print("✅ Content retrieval successful!")
            print(f"📚 Book ID: {result.get('book_id')}")
            content = result.get('lesson_content', '')
            print(f"📝 Content length: {len(content)} characters")
            print(f"📄 Content preview: {content[:200]}...")
        else:
            print(f"❌ Content retrieval failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error getting content: {e}")

if __name__ == "__main__":
    try:
        print("🧪 Testing Optimized PDF Processing System")
        print("=" * 50)
        test_pdf_upload()
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

@echo off
chcp 65001 >nul
echo Stopping FastAPI Server...

REM Method 1: Kill process on port 8000
echo Checking port 8000...
netstat -ano | findstr :8000 >nul
if not errorlevel 1 (
    echo Found process on port 8000, stopping...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8000') do (
        taskkill /PID %%a /F >nul 2>&1
        echo Process %%a stopped
    )
) else (
    echo No process found on port 8000
)

REM Method 2: Kill all uvicorn processes
echo Stopping uvicorn processes...
wmic process where "commandline like '%%uvicorn%%'" delete >nul 2>&1

REM Method 3: Kill FastAPI related python processes
tasklist | findstr python.exe >nul
if not errorlevel 1 (
    echo Checking Python processes...
    wmic process where "name='python.exe' and commandline like '%%main:app%%'" delete >nul 2>&1
)

echo FastAPI shutdown complete
pause
